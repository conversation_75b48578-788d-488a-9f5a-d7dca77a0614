import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import { useNavigate, useParams, useLocation } from 'react-router-dom'
import Cookies from 'js-cookie';
import { message } from 'antd'
import MessageList from './chat/MessageList';
import ChatInput from './chat/ChatInput';
import FloatingButtons from './FloatingButtons';
import Header from './Header';
import Sidebar from './Sidebar';
import ConversationsPage from './ConversationPage';
import PageTitle from './PageTitle';
import ChatDetailHeader from './chat/ChatDetailHeader';


import { DifyApi, IFileLocal } from '../api/src/dify-api';
import { generateUUID, isMobile } from '../api/src/utils';
import { XAiApi } from "../api/src/xai-api";
import { getFileTypeByName } from '../api/src/utils/file-type';
import { ChatUploadFileItem } from './chat/ChatFileUpload';
import { useSimpleTranslation, useI18nRouter } from '../i18n/simple-hooks';


// 工作流节点数据
interface WorkflowNode {
  id: string
  type: string
  title: string
  status: 'running' | 'success' | 'error'
  parallel_id?: string
  created_at: number
  error?: string
  outputs?: any
  process_data?: any
  inputs?: any
  // 新增：节点分类字段
  displayCategory?: 'left' | 'right' | 'result' | 'none'
}

// 节点显示名称函数将在组件内部定义以使用翻译

// 根据title字段判断节点的显示分类
const getNodeDisplayCategory = (title: string): 'left' | 'right' | 'result' | 'none' => {
  if (!title) return 'none'

  const upperTitle = title.toUpperCase()

  // 检查关键字（不区分大小写）
  if (upperTitle.includes('SHOW')) {
    return 'left'
  } else if (upperTitle.includes('DETAIL')) {
    return 'right'
  } else if (upperTitle.includes('RESULT')) {
    return 'result'
  }

  // 如果不包含任何指定关键字，返回none（不显示）
  return 'none'
}

// 文件上传相关类型定义
interface UploadFileItem {
  uid: string
  name: string
  status: 'uploading' | 'done' | 'error'
  size: number
  type: 'document' | 'image' | 'audio' | 'video'
  originFileObj?: { uid: string }
  percent: number
  transfer_method: 'local_file'
  upload_file_id?: string
  error?: string
}

// 定义ChatMessage类型
interface ChatMessage {
  id: string
  type: 'user' | 'assistant'
  content: string
  timestamp: Date
  thinking?: string
  steps?: string[]
  isGenerating?: boolean
  searchResults?: SearchResult[]
  browsingResults?: string[]
  // 新增：评审相关字段
  isReviewing?: boolean
  reviewContent?: string
  // 新增：复审相关字段
  isSecondReviewing?: boolean
  secondReviewContent?: string
  // 新增：工作流相关字段
  workflowNodes?: WorkflowNode[]
  workflowStatus?: 'running' | 'finished'
  // 新增：文件附件字段
  files?: UploadFileItem[]
  // 新增：特定节点答案相关字段
  nodeAnswer?: string
  isNodeAnswerGenerating?: boolean
  // 新增：分类显示的节点数据
  leftPanelNodes?: WorkflowNode[]    // 显示在左侧面板的节点
  rightPanelNodes?: WorkflowNode[]   // 显示在右侧面板的节点
  resultNodes?: WorkflowNode[]       // 显示在结果组件的节点
}

interface SearchResult {
  title: string
  type: string
  url: string
  description?: string
}

// 获取动态配置的函数
const getApiConfig = () => {
  // 获取域名
  const hostname = window.location.hostname
  let apiBase = 'http://localhost:3000'

  // 根据域名动态设置 API 基础地址
  if (hostname === 'ai.medon.com.cn') {
    apiBase = 'https://ai.medon.com.cn/dev-api'
  } else if (hostname === 'ai.medsci.cn') {
    apiBase = 'https://ai.medsci.cn/dev-api'
  }

  // 获取用户信息 - 可以从 localStorage、sessionStorage 或其他地方获取
  const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}')
  const user = userInfo.userName || '用户'

  return {
    apiBase,
    user,
    yudaoToken: Cookies.get('yudaoToken') || ''
  }
}

const ChatDetail = () => {
  const { t } = useSimpleTranslation()
  const { currentLanguage } = useI18nRouter()
  const navigate = useNavigate();
  const location = useLocation();

  // 支持新的国际化路由结构: /:lang/:appName/:sessionId
  const { appName, sessionId, packageKey, conversationId: urlConversationId } = useParams<{
    appName?: string,
    sessionId?: string,
    packageKey?: string,
    conversationId?: string
  }>();

  // 节点类型到名称的映射函数（使用翻译）
  const getNodeDisplayName = useCallback((nodeType: string, title?: string): string => {
    // 如果有自定义标题，优先使用标题
    if (title && title.trim()) {
      return title
    }

    // 使用翻译映射
    const translationKey = `workflow.nodes.${nodeType.replace('-', '')}`
    const translatedName = t(translationKey) as string

    // 如果翻译存在且不等于key本身，返回翻译
    if (translatedName !== translationKey) {
      return translatedName
    }

    // 否则返回原始类型
    return nodeType
  }, [t])

  // 处理新的国际化路由结构和向后兼容性
  // 新格式: /:lang/:appName/:sessionId (如 /zh/novax-base/session-123)
  // 旧格式: /chat/:packageKey/:conversationId (如 /chat/novax-base/session-123)

  let actualPackageKey: string | undefined;
  let actualConversationId: string | undefined;

  if (appName) {
    // 新的国际化路由格式
    actualPackageKey = appName;

    // 检测是否为新对话状态：
    // 1. sessionId是"new"（新对话标识符）
    // 2. sessionId是"chat"（兼容旧的中间状态）
    const isNewConversation = sessionId === 'new' ||
                             sessionId === 'chat';

    actualConversationId = isNewConversation ? undefined : sessionId;
  } else if (packageKey) {
    // 旧的路由格式
    const isLegacyFormat = packageKey && !urlConversationId && packageKey.length > 20; // conversationId通常很长
    actualPackageKey = isLegacyFormat ? undefined : packageKey;
    actualConversationId = isLegacyFormat ? packageKey : urlConversationId;
  }
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [conversationId, setConversationId] = useState<string>('');
  const [taskId, setTaskId] = useState<string>('');
  const [appInfo, setAppInfo] = useState<any>(null);

  // 使用 useRef 存储最新的节点ID，避免异步问题
  const currentNodeIdRef = useRef<string>('');

  // 使用ref来跟踪是否已经处理过初始数据，避免重复处理
  const hasInitialDataProcessed = useRef(false);

  // 使用ref来跟踪当前正在处理的查询，避免重复处理相同查询
  const currentProcessingQuery = useRef<string | null>(null);

  // 使用ref来跟踪是否是从新对话开始的会话，避免误加载历史记录
  const isFromNewConversation = useRef(false);



  // 文件上传相关状态
  const [uploadFiles, setUploadFiles] = useState<ChatUploadFileItem[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [appParam, setAppParam] = useState<any>(null);
  const [difyApi, setDifyApi] = useState<DifyApi | null>(null);
  const [isAppInitializing, setIsAppInitializing] = useState(true);
  const [appInitError, setAppInitError] = useState<string | null>(null);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [historyLoadError, setHistoryLoadError] = useState<string | null>(null);
  const [loadedHistoryConversationId, setLoadedHistoryConversationId] = useState<string | null>(null);

  // 初始化 XAi API 实例（使用useMemo避免重复创建）
  const xAiApi = useMemo(() => {
    const apiConfig = getApiConfig();
    return new XAiApi({
      user: apiConfig.user,
      apiBase: apiConfig.apiBase,
      yudaoToken: apiConfig.yudaoToken
    });
  }, []); // 空依赖数组，只在组件挂载时创建一次

  // Header 组件需要的状态管理
  const [sidebarOpen, setSidebarOpen] = useState(() => {
    // PC端默认显示侧边栏，移动端默认隐藏
    return window.innerWidth >= 768; // md断点是768px
  });
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [searchModalOpen, setSearchModalOpen] = useState(false);
  const [conversationModalOpen, setConversationModalOpen] = useState(false);
  const [appList, setAppList] = useState<any[]>([]);
  const [currentApp, setCurrentApp] = useState<any>(null);

  // PC端Header滚动状态管理
  const [isHeaderVisible, setIsHeaderVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // 获取国际化路由钩子
  const { navigateToApp } = useI18nRouter();



  // 应用选择处理函数
  const onAppSelect = useCallback((appUuid: string) => {
    // 通过国际化路由导航切换应用，而不是直接设置状态
    const targetApp = appList.find(app => app.appUuid === appUuid || app.appNameEn === appUuid);
    if (targetApp) {
      console.log('切换应用到:', targetApp.appNameEn);
      navigateToApp(targetApp.appNameEn);
    }
  }, [appList, navigateToApp]);



  // 获取应用列表
  useEffect(() => {
    const fetchAppList = async () => {
      try {
        const novaxs = await xAiApi.getAppByConfigKey({ configKey: 'novax_apps' });
        const elavaxs = await xAiApi.getAppByConfigKey({ configKey: 'elavax_apps' });
        const apps = [...novaxs, ...elavaxs];
        setAppList(apps);

        // 根据当前路径设置当前应用
        if (actualPackageKey && apps.length > 0) {
          const foundApp = apps.find((app: any) => app.appNameEn === actualPackageKey);
          if (foundApp) {
            setCurrentApp(foundApp);
          } else {
            setCurrentApp(apps[0]); // 默认使用第一个应用
          }
        } else if (apps.length > 0) {
          setCurrentApp(apps[0]); // 默认使用第一个应用
        }
      } catch (error) {
        console.error('获取应用列表失败:', error);
      }
    };

    fetchAppList();
  }, [xAiApi, actualPackageKey]);





  // 检查认证状态
  useEffect(() => {
    const checkAuth = () => {
      const token = Cookies.get('yudaoToken');
      if (!token) {
        navigate('/');
      }
    };

    checkAuth();
  }, [navigate]);

  // PC端Header滚动监听
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // 向下滚动超过100px时隐藏，向上滚动时显示
      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsHeaderVisible(false);
      } else {
        setIsHeaderVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    // 添加滚动监听器
    window.addEventListener('scroll', handleScroll, { passive: true });

    // 清理函数
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [lastScrollY]);

  // ESC键关闭弹窗功能
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        // 按优先级关闭弹窗：会话历史 > 搜索 > 侧边栏
        if (conversationModalOpen) {
          setConversationModalOpen(false)
        } else if (searchModalOpen) {
          setSearchModalOpen(false)
        } else if (sidebarOpen) {
          setSidebarOpen(false)
        }
      }
    }

    // 只在有弹窗打开时添加监听器
    if (conversationModalOpen || searchModalOpen || sidebarOpen) {
      document.addEventListener('keydown', handleKeyDown)
      return () => {
        document.removeEventListener('keydown', handleKeyDown)
      }
    }
  }, [conversationModalOpen, searchModalOpen, sidebarOpen]);

  // 监听窗口大小变化，调整侧边栏状态
  useEffect(() => {
    const handleResize = () => {
      const isDesktop = window.innerWidth >= 768
      // 如果从移动端切换到PC端，自动显示侧边栏并重置折叠状态
      if (isDesktop && !sidebarOpen) {
        setSidebarOpen(true)
        setSidebarCollapsed(false)
      }
      // 如果从PC端切换到移动端，自动隐藏侧边栏并重置折叠状态
      else if (!isDesktop && sidebarOpen) {
        setSidebarOpen(false)
        setSidebarCollapsed(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [sidebarOpen]);

  // 转换IFileLocal格式到ChatUploadFileItem格式
  const convertIFileLocalToChatUploadFileItem = useCallback((files: IFileLocal[]): ChatUploadFileItem[] => {
    return files.map((file, index) => {
      const detectedType = getFileTypeByName(file.name);
      const fileType: 'document' | 'image' | 'audio' | 'video' =
        (detectedType as unknown as 'document' | 'image' | 'audio' | 'video') || 'document';

      return {
        uid: file.upload_file_id || `converted-${Date.now()}-${index}`,
        name: file.name,
        status: 'done' as const,
        size: file.size,
        type: fileType,
        percent: 100,
        transfer_method: 'local_file' as const,
        upload_file_id: file.upload_file_id
      };
    });
  }, []);

  // 检查文件是否为IFileLocal格式
  const isIFileLocalArray = useCallback((files: any[]): files is IFileLocal[] => {
    return files.length > 0 && 'upload_file_id' in files[0] && !('uid' in files[0]);
  }, []);

  // 获取初始数据（从sessionStorage或URL参数）
  const [initialData, setInitialData] = useState<{
    query?: string
    appNameEn?: string
    appUuid?: string
    packageKey?: string
    files?: IFileLocal[] | ChatUploadFileItem[]
  } | null>(null)
  // 获取初始数据
  useEffect(() => {
    const loadInitialData = () => {
      // 从sessionStorage获取初始数据
      const storedData = sessionStorage.getItem('chatInitialData')

      if (storedData) {
        try {
          const parsedData = JSON.parse(storedData)

          // 验证数据有效性（24小时内）
          const now = Date.now()
          const dataAge = now - (parsedData.timestamp || 0)
          const maxAge = 24 * 60 * 60 * 1000 // 24小时

          if (dataAge < maxAge) {
            setInitialData(parsedData)
            // 清理sessionStorage中的数据（一次性使用）
            sessionStorage.removeItem('chatInitialData')
          }
        } catch (error) {
          console.error('解析会话数据失败:', error)
        }
      }
    }

    loadInitialData()
  }, [])

  // 使用ref来跟踪是否已经初始化，避免重复调用
  const hasInitialized = useRef(false);
  const lastPackageKey = useRef<string>('');

  // 统一的应用初始化逻辑，避免重复调用
  useEffect(() => {
    const currentPackageKey = actualPackageKey || initialData?.packageKey || initialData?.appNameEn || initialData?.appUuid || 'novax-base';

    // 防止重复初始化：检查是否已经初始化过相同的packageKey
    if (hasInitialized.current && lastPackageKey.current === currentPackageKey) {
      console.log('💬 ChatDetail.tsx - 跳过重复初始化', {
        currentPackageKey,
        lastPackageKey: lastPackageKey.current,
        hasInitialized: hasInitialized.current
      });
      return;
    }

    const initializeApp = async () => {
      try {
        setIsAppInitializing(true);
        setAppInitError(null);

        const token = Cookies.get('yudaoToken');
        if (!token) {
          console.warn(t('chat.noAuthTokenWarning'));
          setAppInitError(t('chat.noAuthToken'));
          setIsAppInitializing(false);
          return;
        }

        // 确定要使用的packageKey，优先级：URL参数 > sessionStorage > 默认值
        let targetPackageKey = currentPackageKey;

        const appInfo = await xAiApi.getAppByNameEn({
          appUuid: targetPackageKey
        });
        setAppInfo(appInfo);

        // 获取应用参数（包含文件上传配置）
        if (appInfo?.dAppUuid) {
          const currentApiConfig = getApiConfig()
          const difyApiWithAppId = new DifyApi({
            user: currentApiConfig.user,
            apiBase: currentApiConfig.apiBase,
            yudaoToken: token,
            appId: appInfo.dAppUuid
          });

          // 设置difyApi实例
          setDifyApi(difyApiWithAppId);

          console.log('💬 ChatDetail.tsx - 调用 getAppParameters 接口', {
            appId: appInfo.dAppUuid,
            appName: appInfo.appName,
            timestamp: new Date().toISOString(),
            packageKey: targetPackageKey
          });
          const parameters = await difyApiWithAppId.getAppParameters();
          setAppParam(parameters);
          console.log('💬 ChatDetail.tsx - getAppParameters 调用完成', parameters);
        }

        // 标记已初始化
        hasInitialized.current = true;
        lastPackageKey.current = targetPackageKey;
        setIsAppInitializing(false);

      } catch (error: any) {
        console.error(t('chat.refreshInitFailed'), error);
        setIsAppInitializing(false);

        let errorMessage = t('chat.initializationFailed');
        if (error?.code === 401) {
          errorMessage = t('chat.authFailed');
        } else if (error?.message) {
          errorMessage = error.message;
        }

        setAppInitError(errorMessage);
        message.error(errorMessage);
      }
    };

    initializeApp();
  }, [initialData, actualPackageKey])

  // 页面刷新时也需要初始化应用（没有initialData的情况）
  useEffect(() => {
    if (!initialData && !appInfo) {
      const initializeAppOnRefresh = async () => {
        try {
          setIsAppInitializing(true);
          setAppInitError(null);

          const token = Cookies.get('yudaoToken');
          if (!token) {
            console.warn(t('chat.noAuthTokenWarning'));
            setAppInitError(t('chat.noAuthToken'));
            setIsAppInitializing(false);
            return;
          }

          // 使用packageKey或默认值
          const targetPackageKey = actualPackageKey || 'novax-base';

          const appInfo = await xAiApi.getAppByNameEn({
            appUuid: targetPackageKey
          });
          setAppInfo(appInfo);

          // 获取应用参数（包含文件上传配置）
          if (appInfo?.dAppUuid) {
            const currentApiConfig = getApiConfig()
            const difyApiWithAppId = new DifyApi({
              user: currentApiConfig.user,
              apiBase: currentApiConfig.apiBase,
              yudaoToken: token,
              appId: appInfo.dAppUuid
            });

            // 设置difyApi实例
            setDifyApi(difyApiWithAppId);

            const parameters = await difyApiWithAppId.getAppParameters();
            setAppParam(parameters);
          }

          setIsAppInitializing(false);

        } catch (error: any) {
          console.error(t('chat.refreshInitFailed'), error);
          setIsAppInitializing(false);

          let errorMessage = t('chat.initializationFailed');
          if (error?.code === 401) {
            errorMessage = t('chat.authFailed');
          } else if (error?.message) {
            errorMessage = error.message;
          }

          setAppInitError(errorMessage);
          message.error(errorMessage);
        }
      };

      initializeAppOnRefresh();
    }
  }, [])

  // 处理初始消息和文件 - 只处理一次
  useEffect(() => {
    if (initialData?.query && messages.length === 0 && !hasInitialDataProcessed.current) {
      hasInitialDataProcessed.current = true;
      // 标记这是从新对话开始的会话
      isFromNewConversation.current = true;

      let convertedFiles: ChatUploadFileItem[] = [];

      // 如果有初始文件，转换格式但只用于显示在用户消息中，不设置到上传文件列表
      if (initialData.files && initialData.files.length > 0) {
        if (isIFileLocalArray(initialData.files)) {
          // 从首页传来的IFileLocal格式，需要转换
          convertedFiles = convertIFileLocalToChatUploadFileItem(initialData.files);
        } else {
          // 已经是ChatUploadFileItem格式
          convertedFiles = initialData.files as ChatUploadFileItem[];
        }
        // 注意：不再调用 setUploadFiles(convertedFiles)，避免在输入框中重复显示
      }

      // 添加用户初始消息（包含附件信息）
      const userMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'user',
        content: initialData.query,
        timestamp: new Date(),
        files: convertedFiles.length > 0 ? [...convertedFiles] : undefined
      }
      setMessages([userMessage])

      // 确保上传文件列表为空，用户可以重新上传新文件
      setUploadFiles([]);
    }
  }, [initialData])

  // 使用ref来跟踪是否已经发送过初始消息，避免重复调用
  const hasInitialMessageSent = useRef(false)

  // 当difyApi初始化完成且有初始查询时，自动发送消息（只发送一次）
  useEffect(() => {
    if (difyApi && initialData?.query && !hasInitialMessageSent.current && hasInitialDataProcessed.current) {
      // 确保初始数据已经处理完成，且只发送一次
      hasInitialMessageSent.current = true
      sendMessageToAPI(initialData.query)
    }
  }, [difyApi, initialData])

  // 模拟打字效果的函数
  const simulateTyping = async (messageId: string, fullContent: string) => {
    const words = fullContent.split(' ')
    let currentContent = ''
    
    for (let i = 0; i < words.length; i++) {
      currentContent += (i === 0 ? '' : ' ') + words[i]
      
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId
            ? { ...msg, content: currentContent, isGenerating: i < words.length - 1 }
            : msg
        )
      )
      
      // 根据内容类型调整延迟时间
      const delay = words[i].includes('\n') ? 100 : 
                   words[i].includes('`') ? 50 : 
                   words[i].length > 10 ? 80 : 30
      
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  // 模拟思考过程的逐步显示
  const simulateThinking = useCallback(async (messageId: string, content: string) => {
    const words = content.split(' ')
    let currentContent = ''
    
    for (let i = 0; i < words.length; i++) {
      currentContent += (i > 0 ? ' ' : '') + words[i]
      
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId
            ? { ...msg, thinking: currentContent }
            : msg
        )
      )
      
      // 动态延迟：短词快一些，长词慢一些
      const delay = Math.max(50, Math.min(200, words[i].length * 20))
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }, [])

  // 防抖更新内容的引用
  const contentUpdateTimeoutRef = useRef<number | null>(null)
  const pendingContentRef = useRef<{[messageId: string]: string}>({})

  // 优化的节点更新函数 - 减少不必要的对象创建和重新渲染
  const updateWorkflowNodeInArray = useCallback((nodeId: string, updates: Partial<WorkflowNode>) => {
    return (nodes: WorkflowNode[] | undefined) => {
      if (!nodes || nodes.length === 0) return nodes;

      let hasChanges = false;
      const updatedNodes = nodes.map(node => {
        if (node.id === nodeId) {
          // 检查是否真的有变化，避免不必要的更新
          let needsUpdate = false;

          if (updates.outputs) {
            const currentAnswer = node.outputs?.answer || '';
            const newAnswer = updates.outputs.answer || '';
            if (currentAnswer !== newAnswer) {
              needsUpdate = true;
            }
          }

          if (updates.status && node.status !== updates.status) {
            needsUpdate = true;
          }

          if (needsUpdate) {
            hasChanges = true;
            return {
              ...node,
              ...updates,
              outputs: updates.outputs ? { ...node.outputs, ...updates.outputs } : node.outputs
            };
          }
        }
        return node;
      });

      return hasChanges ? updatedNodes : nodes;
    };
  }, []);

  // 平滑累加内容更新 - 智能检测增量或完整内容，使用防抖避免频繁更新
  const smoothAppendContent = useCallback((messageId: string, newAnswer: string, isComplete: boolean = false) => {
    // 立即更新待处理内容
    const currentContent = pendingContentRef.current[messageId] || ''

    // 如果新答案以当前内容开头，说明是完整内容（包含之前的部分）
    if (newAnswer.startsWith(currentContent)) {
      pendingContentRef.current[messageId] = newAnswer
    } else {
      // 否则，将新答案追加到现有内容后面
      pendingContentRef.current[messageId] = currentContent + newAnswer
    }

    // 如果是完成状态，立即更新
    if (isComplete) {
      if (contentUpdateTimeoutRef.current) {
        clearTimeout(contentUpdateTimeoutRef.current)
        contentUpdateTimeoutRef.current = null
      }

      setMessages(prev =>
        prev.map(msg => {
          if (msg.id === messageId) {
            return {
              ...msg,
              content: pendingContentRef.current[messageId] || '',
              workflowNodes: msg.workflowNodes || [],
              isGenerating: false
            }
          }
          return msg
        })
      )

      // 清理待处理内容
      delete pendingContentRef.current[messageId]
      return
    }

    // 使用防抖更新，减少渲染频率
    if (contentUpdateTimeoutRef.current) {
      clearTimeout(contentUpdateTimeoutRef.current)
    }

    contentUpdateTimeoutRef.current = setTimeout(() => {
      setMessages(prev =>
        prev.map(msg => {
          if (msg.id === messageId) {
            return {
              ...msg,
              content: pendingContentRef.current[messageId] || msg.content,
              isGenerating: msg.isGenerating,

            }
          }
          return msg
        })
      )
      contentUpdateTimeoutRef.current = null
    }, 100) // 100ms 防抖延迟
  }, [])

  // 清理防抖定时器
  useEffect(() => {
    return () => {
      if (contentUpdateTimeoutRef.current) {
        clearTimeout(contentUpdateTimeoutRef.current)
      }
    }
  }, [])



  // 当actualConversationId变化时，重置加载状态
  useEffect(() => {
    if (actualConversationId !== loadedHistoryConversationId) {
      // 如果conversationId变化了，清除之前的状态
      if (loadedHistoryConversationId && actualConversationId !== loadedHistoryConversationId) {
        setMessages([]);
        setLoadedHistoryConversationId(null);
      }
    }
  }, [actualConversationId, loadedHistoryConversationId])

  // 加载历史消息的函数
  const loadConversationHistory = useCallback(async (conversationId: string) => {
    if (!difyApi) {
      return;
    }

    try {
      setIsLoadingHistory(true);
      setHistoryLoadError(null);

      const historyResponse = await difyApi.getConversationHistory(conversationId);

      if (historyResponse?.data && Array.isArray(historyResponse.data)) {
        // 将API返回的消息转换为ChatMessage格式
        const historyMessages: ChatMessage[] = [];

        historyResponse.data.forEach((item) => {
          // 验证必要字段
          if (!item.id || !item.created_at) {
            return;
          }

          // 添加用户消息
          if (item.query && item.query.trim()) {
            historyMessages.push({
              id: `${item.id}_user`,
              type: 'user',
              content: item.query,
              timestamp: new Date(item.created_at * 1000),
              files: item.message_files && Array.isArray(item.message_files) && item.message_files.length > 0 ?
                item.message_files.map((file: any) => ({
                  uid: file.id || `file_${Date.now()}_${Math.random()}`,
                  name: file.name || t('chat.unknownFile'),
                  status: 'done' as const,
                  size: file.size || 0,
                  type: (file.type && ['document', 'image', 'audio', 'video'].includes(file.type)) ? file.type : 'document' as const,
                  percent: 100,
                  transfer_method: 'local_file' as const,
                  upload_file_id: file.id
                })) : undefined
            });
          }

          // 添加AI回复消息
          if (item.answer && item.answer.trim()) {
            historyMessages.push({
              id: `${item.id}_ai`,
              type: 'assistant',
              content: item.answer,
              timestamp: new Date(item.created_at * 1000),
              isGenerating: false,
              thinking: item.agent_thoughts && Array.isArray(item.agent_thoughts) && item.agent_thoughts.length > 0 ?
                item.agent_thoughts.map((thought: any) => thought.thought || '').filter(Boolean).join('\n') : undefined,
            });
          }
        });

        // 按时间戳排序（从早到晚）
        historyMessages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

        // 设置历史消息到状态中
        setMessages(historyMessages);
        setConversationId(conversationId);
        setLoadedHistoryConversationId(conversationId);

        // 延迟滚动到最新消息，确保DOM已更新
        setTimeout(() => {
          // 查找MessageList组件的容器并滚动到底部
          const messageContainer = document.querySelector('.flex-1.px-6.py-6.pb-48.relative');
          if (messageContainer) {
            messageContainer.scrollTop = messageContainer.scrollHeight;
          } else {
            // 备用方案：滚动到页面底部
            window.scrollTo(0, document.body.scrollHeight);
          }
        }, 100);
      } else {
        console.warn(t('chat.historyResponseFormatError'), historyResponse);
        setHistoryLoadError(t('chat.historyLoadFailed'));
      }

    } catch (error: any) {
      console.error(t('chat.loadHistoryFailed'), error);

      let errorMessage = t('chat.loadHistoryFailed');
      if (error?.code === 401) {
        errorMessage = t('chat.authFailed');
      } else if (error?.message) {
        errorMessage = error.message;
      }

      setHistoryLoadError(errorMessage);
      message.error(errorMessage);
    } finally {
      setIsLoadingHistory(false);
    }
  }, [difyApi]);

  // 检查URL中的conversationId并加载历史消息
  useEffect(() => {
    const loadHistoryIfNeeded = async () => {
      // 只有当difyApi已初始化且URL中有conversationId时才加载历史消息
      // 但是如果这是从新对话开始的会话，则不加载历史记录
      if (difyApi && actualConversationId && !isFromNewConversation.current) {
        // 检查是否已经加载过这个conversationId（避免重复加载）
        if (loadedHistoryConversationId !== actualConversationId) {
          await loadConversationHistory(actualConversationId);
        }
      }
    };

    loadHistoryIfNeeded();
  }, [difyApi, actualConversationId]);

  // 文件验证错误处理
  const handleValidationError = useCallback((error: string) => {
    // 文件验证失败
  }, []);

  // 文件上传处理函数
  const handleFileUpload = useCallback(async (files: File[]) => {
    // 如果appParam已加载且明确禁用了文件上传，则阻止上传
    if (appParam && appParam.file_upload && appParam.file_upload.enabled === false) {
      message.error(t('chat.fileUploadNotSupported'));
      return;
    }

    if (!difyApi) {
      message.error(t('chat.appNotInitialized'));
      return;
    }

    // 检查当前文件数量限制（使用动态配置值）
    const maxFiles = appParam?.file_upload?.number_limits || 2;
    const currentFileCount = uploadFiles.length;
    const availableSlots = maxFiles - currentFileCount;

    if (availableSlots <= 0) {
      message.error(t('chat.maxFilesReached').replace('{count}', maxFiles.toString()));
      return;
    }

    // 检查新上传文件数量是否超过剩余可用槽位
    if (files.length > availableSlots) {
      message.error(t('chat.maxFilesExceeded')
        .replace('{available}', availableSlots.toString())
        .replace('{max}', maxFiles.toString()));
      return;
    }

    setIsUploading(true);

    try {
      // 由于前面已经检查过数量，这里直接使用所有文件
      const limitFiles = files;

      const newUploadFiles: ChatUploadFileItem[] = [];

      // 为每个文件创建上传项
      for (const file of limitFiles) {
        const uid = `upload-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        const detectedType = getFileTypeByName(file.name);
        const fileType: 'document' | 'image' | 'audio' | 'video' =
          (detectedType as unknown as 'document' | 'image' | 'audio' | 'video') || 'document';

        const uploadItem: ChatUploadFileItem = {
          uid,
          name: file.name,
          status: 'uploading',
          size: file.size,
          type: fileType || 'document',
          originFileObj: { uid },
          percent: 0,
          transfer_method: 'local_file'
        };

        newUploadFiles.push(uploadItem);
      }

      // 添加到上传列表
      setUploadFiles(prev => [...prev, ...newUploadFiles]);

      // 逐个上传文件
      for (let i = 0; i < limitFiles.length; i++) {
        const file = limitFiles[i];
        const uploadItem = newUploadFiles[i];

        try {
          // 模拟上传进度
          const progressInterval = setInterval(() => {
            setUploadFiles(prev =>
              prev.map(item =>
                item.uid === uploadItem.uid
                  ? { ...item, percent: Math.min(item.percent + 10, 90) }
                  : item
              )
            );
          }, 200);

          // 添加重试机制
          let retryCount = 0;
          const maxRetries = 3;
          let uploadResult: any;

          while (retryCount <= maxRetries) {
            try {
              uploadResult = await difyApi.uploadFile(file);
              break; // 成功则跳出重试循环
            } catch (retryError) {
              retryCount++;
              if (retryCount > maxRetries) {
                throw retryError; // 超过重试次数则抛出错误
              }
              await new Promise(resolve => setTimeout(resolve, 1000 * retryCount)); // 递增延迟
            }
          }

          clearInterval(progressInterval);

          if (uploadResult.code !== 0) {
            throw new Error(uploadResult.msg || t('chat.uploadFailed'));
          }

          // 上传成功，更新状态
          setUploadFiles(prev =>
            prev.map(item =>
              item.uid === uploadItem.uid
                ? {
                    ...item,
                    status: 'done',
                    percent: 100,
                    upload_file_id: uploadResult.data.id
                  }
                : item
            )
          );

          console.log(t('chat.uploadSuccess')
            .replace('{fileName}', file.name)
            .replace('{fileId}', uploadResult.data.id));

        } catch (error) {

          let errorMessage = t('chat.uploadFailed');
          if (error instanceof Error) {
            if (error.message.includes('network') || error.message.includes('fetch')) {
              errorMessage = t('chat.networkError');
            } else if (error.message.includes('timeout')) {
              errorMessage = t('chat.uploadTimeout');
            } else if (error.message.includes('size')) {
              errorMessage = t('chat.fileSizeLimit');
            } else {
              errorMessage = error.message;
            }
          }

          // 上传失败，更新状态
          setUploadFiles(prev =>
            prev.map(item =>
              item.uid === uploadItem.uid
                ? {
                    ...item,
                    status: 'error',
                    error: errorMessage
                  }
                : item
            )
          );

          message.error(`${t('common.file')} ${file.name} ${errorMessage}`);
        }
      }

    } catch (error) {
      console.error('文件上传处理失败:', error);
      message.error(t('chat.uploadFailed'));
    } finally {
      setIsUploading(false);
    }
  }, [difyApi, appParam]);

  // 文件列表变更处理
  const handleFilesChange = useCallback((files: ChatUploadFileItem[]) => {
    setUploadFiles(files);
  }, []);

  // 模拟搜索结果的逐步显示
  const simulateSearchResults = useCallback(async (messageId: string, results: SearchResult[]) => {
    for (let i = 0; i < results.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 300)) // 每个结果间隔300ms
      
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId
            ? { ...msg, searchResults: results.slice(0, i + 1) }
            : msg
        )
      )
    }
  }, [])

  // 模拟浏览结果的逐步显示
  const simulateBrowsingResults = useCallback(async (messageId: string, results: string[]) => {
    for (let i = 0; i < results.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 500)) // 每个浏览结果间隔500ms
      
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId
            ? { ...msg, browsingResults: results.slice(0, i + 1) }
            : msg
        )
      )
    }
  }, [])
  
  // 新增：模拟评审内容的逐步显示
  const simulateReviewContent = useCallback(async (messageId: string, content: string) => {
    const words = content.split(' ')
    let currentContent = ''
    
    for (let i = 0; i < words.length; i++) {
      currentContent += (i > 0 ? ' ' : '') + words[i]
      
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId
            ? { ...msg, reviewContent: currentContent }
            : msg
        )
      )
      
      // 评审内容显示稍快一些
      const delay = Math.max(30, Math.min(100, words[i].length * 15))
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }, [])
  
  // 新增：模拟复审内容的逐步显示
  const simulateSecondReviewContent = useCallback(async (messageId: string, content: string) => {
    const words = content.split(' ')
    let currentContent = ''
    
    for (let i = 0; i < words.length; i++) {
      currentContent += (i > 0 ? ' ' : '') + words[i]
      
      setMessages(prev =>
        prev.map(msg =>
          msg.id === messageId
            ? { ...msg, secondReviewContent: currentContent }
            : msg
        )
      )
      
      // 复审内容显示稍快一些
      const delay = Math.max(30, Math.min(100, words[i].length * 15))
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }, [])

  const generateAssistantResponse = async (query: string) => {
    setIsGenerating(true)

    const assistantMessage: ChatMessage = {
      id: `${Date.now().toString()}_ai`,
      type: 'assistant',
      content: '',
      timestamp: new Date(),
      thinking: '',
      steps: [],
      isGenerating: true,
      searchResults: [],
      browsingResults: [],
      // 新增：评审相关字段初始化
      isReviewing: false,
      reviewContent: '',
      // 新增：复审相关字段初始化
      isSecondReviewing: false,
      secondReviewContent: '',
      workflowNodes: [],
      workflowStatus: 'running'
    }

    setMessages(prev => [...prev, assistantMessage])

    // 模拟思考过程
    const thinkingContent = `正在深度分析用户查询："${query}"

🔍 第一步：领域识别与背景分析
- 识别查询涉及的学科领域和研究方向
- 分析当前领域的研究热点和发展趋势
- 评估查询的复杂度和专业程度

🧠 第二步：知识图谱构建
- 构建相关概念的知识网络
- 识别潜在的交叉学科机会
- 分析研究空白和创新点

🎯 第三步：方案匹配策略
- 评估NovaX AI平台各层次服务的适用性
- Base层：适合初步思路启发和方向探索
- Pro层：适合深度个性化研究规划
- Ultra层：适合战略性课题集群设计

📊 第四步：资源整合分析
- 整合相关文献资源和数据库
- 识别潜在合作机构和专家网络
- 评估实施可行性和资源需求

正在为您匹配最优的科研灵感方案...`

    // 模拟搜索结果
    const mockSearchResults: SearchResult[] = [
      {
        title: 'NovaX AI - 您的科研灵感引擎',
        type: 'website',
        url: 'ai.medsci.cn/novax/',
        description: 'NovaX AI科研灵感引擎，构建前瞻性研究蓝图'
      },
      {
        title: 'NovaX Base - 核心思路启发',
        type: 'service',
        url: 'ai.medsci.cn/novax/base',
        description: '基于研究背景进行单次科研方向思路设计'
      },
      {
        title: 'NovaX Pro - 个性化研究规划',
        type: 'service',
        url: 'ai.medsci.cn/novax/pro',
        description: '深度匹配研究基础，提供个性化研究设计方案'
      },
      {
        title: 'NovaX Ultra - 构筑创新生态',
        type: 'service',
        url: 'ai.medsci.cn/novax/ultra',
        description: '战略性课题集群，前瞻性领域洞察'
      },
      {
        title: '科研方向规划与创新思路孵化',
        type: 'docs',
        url: 'ai.medsci.cn/novax/docs'
      }
    ]


    // 逐步显示思考内容
    await new Promise(resolve => setTimeout(resolve, 1200))
    await simulateThinking(assistantMessage.id, thinkingContent)

    // 逐步添加搜索结果
    await new Promise(resolve => setTimeout(resolve, 1000))
    await simulateSearchResults(assistantMessage.id, mockSearchResults)

    // 逐步添加浏览结果
    await new Promise(resolve => setTimeout(resolve, 800))
    const browsingResults = [
      `通过搜索发现，NovaX AI提供了完整的科研灵感引擎服务，包含Base、Pro、Ultra三个层次的研究规划方案，能够为科研工作者提供从思路启发到战略布局的全方位支持。
      基于您提供的研究背景或关注领域，进行单次科研方向思路设计。
      获取创新思路的初步构想，点亮您的研究起点。
      基于您当前的研究基础与可用条件，进行深度匹配。
      提供个性化的研究设计方案与合理的科研实施规划。
      智能匹配潜在的目标平台与发展机遇。
      战略性课题集群： 超越单一思路，为您智能规划多点联动、可持续发展的系列创新研究课题群，构筑您的科研战略版图。
      前瞻性领域洞察： 融合新兴技术与交叉学科趋势，提供未来3~5年高潜力研究方向与战略布局建议，助您抢占先机。
      深度定制与一站式方案： 基于您的需求深度定制，从颠覆性灵感孵化到完整战略方案构建，一站式解决，引领创新范式。`
    ]
    await simulateBrowsingResults(assistantMessage.id, browsingResults)

    // 生成最终回答 - 逐步显示内容
    await new Promise(resolve => setTimeout(resolve, 500))
    const finalAnswer = `# NovaX AI 科研灵感引擎详解

## 🎯 核心发现
NovaX AI 是您的科研灵感引擎，专为构建前瞻性研究蓝图而设计。平台提供三个层次的专业评估方案，每一种都为您提供专业的评估和优化建议。

## 🚀 服务方案体系

### 1. NovaX Base - 核心思路启发
\`\`\`
服务内容：
- 基于研究背景进行单次科研方向思路设计
- 获取创新思路的初步构想
- 点亮您的研究起点
\`\`\`

### 2. NovaX Pro - 个性化研究规划
\`\`\`
服务内容：
- 基于当前研究基础与可用条件进行深度匹配
- 提供个性化的研究设计方案与合理的科研实施规划
- 智能匹配潜在的目标平台与发展机遇
\`\`\`

### 3. NovaX Ultra - 擘画科研未来，构筑创新生态
\`\`\`
服务内容：
- 战略性课题集群：超越单一思路，智能规划多点联动、
  可持续发展的系列创新研究课题群
- 前瞻性领域洞察：融合新兴技术与交叉学科趋势，
  提供未来3~5年高潜力研究方向与战略布局建议
- 深度定制与一站式方案：从颠覆性灵感孵化到完整
  战略方案构建，引领创新范式
\`\`\`

## 🔬 应用场景

### 学术研究
- **高校科研**: 课题申报、研究方向规划
- **博士研究**: 论文选题、创新点挖掘
- **团队协作**: 多学科交叉研究设计

### 产业创新
- **企业研发**: 技术路线图制定
- **创业项目**: 商业模式创新
- **政策制定**: 前瞻性政策研究

## 💡 核心优势

### 智能化程度
1. **AI驱动**: 基于先进的人工智能技术
2. **个性匹配**: 深度理解用户研究背景
3. **前瞻洞察**: 把握未来发展趋势

### 专业性保障
1. **领域专家**: 多学科专家团队支持
2. **数据支撑**: 海量科研数据库
3. **实时更新**: 跟踪最新研究动态

## 📊 价值体现
- **创新效率**: 大幅提升研究创新效率
- **成功概率**: 提高项目成功率和影响力
- **资源优化**: 合理配置研究资源
- **战略布局**: 构建长期发展优势

## 🎉 预期成果
使用NovaX AI后，您将获得：
- 🧠 创新思维的系统性提升
- 📈 研究方向的精准定位
- 🔄 可持续的科研发展路径
- 🌟 前瞻性的战略布局能力

**总结**: NovaX AI是专业的科研灵感引擎，通过AI技术与专家智慧的结合，为科研工作者提供从思路启发到战略规划的全方位支持，助力构建前瞻性研究蓝图。`

    // 新增：模拟评审过程
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // 开始评审阶段
    setMessages(prev =>
      prev.map(msg =>
        msg.id === assistantMessage.id
          ? { ...msg, isReviewing: true }
          : msg
      )
    )
    
    // 模拟评审内容的逐步显示
    const reviewContent = `正在对生成的回答进行全面评审...

✅ 内容准确性检查：验证NovaX AI服务方案的描述是否准确
✅ 逻辑结构评估：检查回答的逻辑层次和结构完整性
✅ 实用性分析：评估回答对用户的实际价值和可操作性
✅ 专业性审核：确保术语使用准确，表达专业规范
✅ 完整性验证：检查是否涵盖了用户查询的所有关键点

评审结论：回答内容全面、准确、实用，符合用户需求，建议发布。`
    
    await simulateReviewContent(assistantMessage.id, reviewContent)
    
    // 评审完成
    setMessages(prev =>
      prev.map(msg =>
        msg.id === assistantMessage.id
          ? { ...msg, isReviewing: false }
          : msg
      )
    )
    
    await new Promise(resolve => setTimeout(resolve, 1300))
    
    // 新增：模拟张博士复审过程
    // 开始复审阶段
    setMessages(prev =>
      prev.map(msg =>
        msg.id === assistantMessage.id
          ? { ...msg, isSecondReviewing: true }
          : msg
      )
    )
    
    // 模拟复审内容的逐步显示
    const secondReviewContent = `张博士正在进行二次复审...

🔍 深度质量检查：对回答内容进行更深层次的质量评估
🔍 创新性评估：检查回答是否具备足够的创新思维和前瞻性
🔍 实用性验证：确保回答对用户具有实际指导价值
🔍 专业度审核：验证专业术语使用的准确性和规范性
🔍 完整性确认：确保回答覆盖了用户需求的所有关键要素
🔍 逻辑性检验：检查回答的逻辑结构是否清晰合理

复审结论：经过深度评估，回答质量优秀，内容全面准确，逻辑清晰，具有很强的实用性和指导价值，建议正式发布。`
    
    await simulateSecondReviewContent(assistantMessage.id, secondReviewContent)
    
    // 复审完成
    setMessages(prev =>
      prev.map(msg =>
        msg.id === assistantMessage.id
          ? { ...msg, isSecondReviewing: false }
          : msg
      )
    )
    
    await new Promise(resolve => setTimeout(resolve, 1300))
    
    // 逐步显示最终回答内容
    await simulateTyping(assistantMessage.id, finalAnswer)

    setIsGenerating(false)
  }

  // 新增：真实的 API 调用函数
  const sendMessageToAPI = async (query: string) => {
    if (!difyApi) {
      message.error('应用未初始化，请稍后重试');
      return;
    }

    // 防止重复调用：如果正在生成中，直接返回
    if (isGenerating) {
      console.warn('消息正在生成中，忽略重复调用');
      return;
    }

    // 防止重复处理相同查询
    if (currentProcessingQuery.current === query) {
      console.warn('相同查询正在处理中，忽略重复调用:', query);
      return;
    }

    try {
      currentProcessingQuery.current = query;
      setTaskId('')
      setIsGenerating(true)

      // 准备文件参数
      const filesForAPI: IFileLocal[] = uploadFiles
        .filter(file => file.status === 'done' && file.upload_file_id)
        .map(file => ({
          name: file.name,
          size: file.size,
          transfer_method: 'local_file' as const,
          upload_file_id: file.upload_file_id!,
          type: file.type as any
        }));

      const assistantMessage: ChatMessage = {
        id: `${Date.now().toString()}_ai`,
        type: 'assistant',
        content: '',
        timestamp: new Date(),
        thinking: '',
        steps: [],
        isGenerating: true,
        searchResults: [],
        browsingResults: [],
        isReviewing: false,
        reviewContent: '',
        isSecondReviewing: false,
        secondReviewContent: '',
        workflowNodes: [],
        workflowStatus: 'running',
        files: uploadFiles.length > 0 ? [...uploadFiles] : undefined
      }

      setMessages(prev => [...prev, assistantMessage])

      // 调用真实的 sendMessage API
      const currentApiConfig = getApiConfig()
      const response = await difyApi.sendMessage({
        conversation_id: conversationId || '',
        inputs: {language: currentLanguage},
        files: filesForAPI,
        user: currentApiConfig.user,
        response_mode: 'streaming',
        query: query,
        appUuid: appInfo?.appUuid || '', // 需要配置实际的应用ID
        requestId: generateUUID(), // 需要配置实际的请求ID
        appId: appInfo?.dAppUuid || '', // 需要配置实际的应用ID
      })

      // 处理流式响应
      if (response.body) {
        const reader = response.body.getReader()
        const decoder = new TextDecoder()
        let buffer = ''

        while (true) {
          const { done, value } = await reader.read()
          if (done) break

          buffer += decoder.decode(value, { stream: true })
          const lines = buffer.split('\n')
          buffer = lines.pop() || ''
          for (const line of lines) {

            if (line.startsWith('data:')) {
              try {
                const data = JSON.parse(line.replace('data:', ''))
                setTaskId(data.task_id)
                if (data.event === 'message') {
                  // 获取当前消息的来源节点ID
                  const currentNodeId = data.from_variable_selector?.[0]
                  const latestNodeId = currentNodeIdRef.current // 获取最新的节点ID

                  // // 检查是否是我们关注的特定节点（使用 ref 中的最新值，避免异步问题）
                  if(currentNodeId && (currentNodeId === latestNodeId ||
                      ['message', 'node-start'].includes(currentNodeId))){
                    // 当消息来自特定节点时，更新对应的工作流节点数据
                    setMessages(prev =>
                      prev.map(msg => {
                        if (msg.id === assistantMessage.id) {
                          const updatedMsg = { ...msg }

                          // 使用优化的节点更新函数
                          const nodeUpdates = {
                            outputs: {
                              answer: (msg.workflowNodes?.find(n => n.id === currentNodeId)?.outputs?.answer || '') + (data.answer || '')
                            }
                          };

                          // 更新所有相关的节点数组
                          updatedMsg.workflowNodes = updateWorkflowNodeInArray(currentNodeId, nodeUpdates)(msg.workflowNodes);
                          updatedMsg.leftPanelNodes = updateWorkflowNodeInArray(currentNodeId, nodeUpdates)(msg.leftPanelNodes);
                          updatedMsg.rightPanelNodes = updateWorkflowNodeInArray(currentNodeId, nodeUpdates)(msg.rightPanelNodes);
                          updatedMsg.resultNodes = updateWorkflowNodeInArray(currentNodeId, nodeUpdates)(msg.resultNodes);

                          // 只有当消息来自 result 类型节点时，才更新主 content
                          const currentNode = updatedMsg.workflowNodes?.find((node: WorkflowNode) => node.id === currentNodeId)
                          if (currentNode?.displayCategory === 'result') {
                            smoothAppendContent(assistantMessage.id, data.answer, false)
                          }

                          return updatedMsg
                        }
                        return msg
                      })
                    )
                  }else{
                    // 对于非特定节点的消息，不更新主 content
                    // 主 content 只应该包含 result 类型节点的内容
                    // 非特定节点消息，不更新主 content
                  }
                  // 使用平滑累加更新，智能处理增量或完整内容，流式输出过程中不改变isGenerating状态

                  // 如果有 conversation_id，保存它并更新URL
                  if (data.conversation_id && !conversationId) {
                    setConversationId(data.conversation_id)
                    // 从新对话页面（带斜杠）更新到历史对话页面（带conversation_id）
                    const currentPackageKey = actualPackageKey || initialData?.packageKey || initialData?.appNameEn || appInfo?.appNameEn || 'novax-base'
                    const newUrl = `/${currentLanguage}/${currentPackageKey}/${data.conversation_id}`

                    // 只有当前URL不是最终格式时才更新
                    const currentPath = location.pathname
                    if (currentPath !== newUrl) {
                      navigate(newUrl, { replace: true })
                    }
                  }
                }
                 if (data.event === 'agent_thought') {
                  // 处理思考过程
                  setMessages(prev =>
                    prev.map(msg =>
                      msg.id === assistantMessage.id
                        ? { ...msg, thinking: data.thought || '' }
                        : msg
                    )
                  )
                }
                 if (data.event === 'workflow_started') {
                  // 工作流开始
                  setMessages(prev =>
                    prev.map(msg =>
                      msg.id === assistantMessage.id
                        ? { ...msg, workflowStatus: 'running' }
                        : msg
                    )
                  )
                }
                 if (data.event === 'workflow_finished') {
                  // 工作流完成 - 更新所有节点状态为完成
                  setMessages(prev =>
                    prev.map(msg => {
                      if (msg.id === assistantMessage.id) {
                        const updatedMsg = { ...msg }

                        // 更新所有运行中的节点状态为成功
                        const updateNodesStatus = (nodes: WorkflowNode[] | undefined) => {
                          return (nodes || []).map(node => {
                            return {
                              ...node,
                              status: node.status === 'running' ? 'success' as const : node.status
                            }
                          })
                        }

                        // 更新所有节点数组
                        updatedMsg.workflowNodes = updateNodesStatus(msg.workflowNodes)
                        updatedMsg.leftPanelNodes = updateNodesStatus(msg.leftPanelNodes)
                        updatedMsg.rightPanelNodes = updateNodesStatus(msg.rightPanelNodes)
                        updatedMsg.resultNodes = updateNodesStatus(msg.resultNodes)

                        updatedMsg.workflowStatus = 'finished'
                        updatedMsg.isGenerating = false
                        updatedMsg.isNodeAnswerGenerating = false



                        return updatedMsg
                      }
                      return msg
                    })
                  )

                  // 同时设置全局的 isGenerating 状态为 false
                  setIsGenerating(false)
                }
                 if (data.event === 'node_started') {
                  // 工作流节点开始
                  setMessages(prev =>
                    prev.map(msg => {
                      if (msg.id === assistantMessage.id) {
                        // 判断节点的显示分类
                        const displayCategory = getNodeDisplayCategory(data.data.title)

                        // 如果节点不需要显示，跳过处理
                        if (displayCategory === 'none') {
                          return msg
                        }

                        currentNodeIdRef.current = data.data.node_id // 同步更新 ref
                        const newNode: WorkflowNode = {
                          id: data.data.node_id,
                          type: data.data.node_type,
                          title: (currentLanguage == 'zh'? data.data.title.split("-")[0]?.split("(")[0]: data.data.title.split("-")[0]?.split("(")[1]?.split(")")[0]) || getNodeDisplayName(data.data.node_type),
                          status: 'running',
                          parallel_id: data.data.parallel_id,
                          created_at: data.data.created_at,
                          displayCategory: displayCategory
                        }

                        // 根据分类更新对应的节点数组
                        const updatedMsg = { ...msg }

                        if (displayCategory === 'left') {
                          const existingNodes = msg.leftPanelNodes || []
                          const nodeIndex = existingNodes.findIndex(n => n.id === data.data.node_id)
                          if (nodeIndex >= 0) {
                            const updatedNodes = [...existingNodes]
                            updatedNodes[nodeIndex] = { ...updatedNodes[nodeIndex], ...newNode }
                            updatedMsg.leftPanelNodes = updatedNodes
                          } else {
                            updatedMsg.leftPanelNodes = [...existingNodes, newNode]
                          }
                        } else if (displayCategory === 'right') {
                          const existingNodes = msg.rightPanelNodes || []
                          const nodeIndex = existingNodes.findIndex(n => n.id === data.data.node_id)
                          if (nodeIndex >= 0) {
                            const updatedNodes = [...existingNodes]
                            updatedNodes[nodeIndex] = { ...updatedNodes[nodeIndex], ...newNode }
                            updatedMsg.rightPanelNodes = updatedNodes
                          } else {
                            updatedMsg.rightPanelNodes = [...existingNodes, newNode]
                          }
                        } else if (displayCategory === 'result') {
                          const existingNodes = msg.resultNodes || []
                          const nodeIndex = existingNodes.findIndex(n => n.id === data.data.node_id)
                          if (nodeIndex >= 0) {
                            const updatedNodes = [...existingNodes]
                            updatedNodes[nodeIndex] = { ...updatedNodes[nodeIndex], ...newNode }
                            updatedMsg.resultNodes = updatedNodes
                          } else {
                            updatedMsg.resultNodes = [...existingNodes, newNode]
                          }

                        }

                        // 同时保持原有的workflowNodes数组以保持兼容性
                        const existingWorkflowNodes = msg.workflowNodes || []
                        const workflowNodeIndex = existingWorkflowNodes.findIndex(n => n.id === data.data.node_id)
                        if (workflowNodeIndex >= 0) {
                          const updatedWorkflowNodes = [...existingWorkflowNodes]
                          updatedWorkflowNodes[workflowNodeIndex] = { ...updatedWorkflowNodes[workflowNodeIndex], ...newNode }
                          updatedMsg.workflowNodes = updatedWorkflowNodes
                        } else {
                          updatedMsg.workflowNodes = [...existingWorkflowNodes, newNode]
                        }
                        return updatedMsg
                      }
                      return msg
                    })
                  )
                }
                 if (data.event === 'node_finished') {
                  // 工作流节点完成 - 更新节点状态和输出数据
                  console.log('node_finished 事件:', {
                    nodeId: data.data.node_id,
                    outputs: data.data.outputs,
                    answer: data.data.answer,
                    status: data.data.status
                  })
                  setMessages(prev =>
                    prev.map(msg => {
                      if (msg.id === assistantMessage.id) {
                        const updatedMsg = { ...msg }

                        // 使用优化的节点更新函数处理 node_finished 事件
                        const nodeUpdates = {
                          status: (data.data.status === 'succeeded' ? 'success' : 'error') as 'success' | 'error',
                          error: data.data.error,
                          inputs: data.data.inputs,
                          outputs: {
                            ...msg.workflowNodes?.find(n => n.id === data.data.node_id)?.outputs,
                            ...(data.data.outputs || {}),
                            // 如果没有 outputs 但有 answer，则使用 answer
                            ...((!data.data.outputs && data.data.answer) ? { answer: data.data.answer } : {})
                          },
                          process_data: data.data.process_data
                        };

                        // 更新所有相关的节点数组
                        updatedMsg.workflowNodes = updateWorkflowNodeInArray(data.data.node_id, nodeUpdates)(msg.workflowNodes);
                        updatedMsg.leftPanelNodes = updateWorkflowNodeInArray(data.data.node_id, nodeUpdates)(msg.leftPanelNodes);
                        updatedMsg.rightPanelNodes = updateWorkflowNodeInArray(data.data.node_id, nodeUpdates)(msg.rightPanelNodes);
                        updatedMsg.resultNodes = updateWorkflowNodeInArray(data.data.node_id, nodeUpdates)(msg.resultNodes);



                        return updatedMsg
                      }
                      return msg
                    })
                  )

                }
                if(data.event === 'error'){
                  message.error(data.message)
                  setIsGenerating(false)
                }
              } catch (e) {
                // 解析响应数据失败
              }
            }
          }
        }
      }

      // 流式输出结束，设置消息完成状态
      setMessages(prev =>
        prev.map(msg =>
          msg.id === assistantMessage.id
            ? { ...msg, isGenerating: false }
            : msg
        )
      )
      setIsGenerating(false)
      currentProcessingQuery.current = null
    } catch (error) {
      console.error(t('chat.sendMessageFailed'), error)

      // 显示错误消息并设置完成状态
      setMessages(prev =>
        prev.map(msg =>
          msg.id === `${Date.now().toString()}_ai`
            ? { ...msg, content: t('chat.sendMessageFailed'), isGenerating: false }
            : msg
        )
      )
      setIsGenerating(false)
      currentProcessingQuery.current = null
    }
  }

  const handleSubmit = () => {
    if ((!inputValue.trim() && uploadFiles.length === 0) || isGenerating) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
      files: uploadFiles.length > 0 ? [...uploadFiles] : undefined
    }

    setMessages(prev => [...prev, userMessage])
    const query = inputValue
    setInputValue('')

    // 调用真实的 API 而不是模拟函数
    sendMessageToAPI(query)

    // 发送后清空文件列表
    setUploadFiles([])
  }
  const handleStop = async () => {
    if (!isGenerating) return

    try {
      if (taskId && difyApi) {
        await difyApi.stopTask(taskId)
      }
    } catch (error) {
      console.error(t('chat.stopTask'), error)
    }

    setIsGenerating(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  // 处理主内容区域点击，在移动端关闭侧边栏
  const handleMainClick = () => {
    const isMobileDevice = window.innerWidth < 768;
    if (isMobileDevice && sidebarOpen) {
      setSidebarOpen(false);
    }
  };

  return (
    <>
      {/* 页面标题 */}
      <PageTitle
        pageType={actualConversationId ? 'chat' : 'newChat'}
        appKey={actualPackageKey}
        chatTitle={messages.length > 0 ? messages[0]?.content?.substring(0, 30) : undefined}
      />

      <div className="flex h-screen" style={{ backgroundColor: 'var(--bg-main)' }}>
        {/* 移动端遮罩层 */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* 左侧边栏 - 响应式，PC端固定定位顶到最上边 */}
        {currentApp && (
          <div className={`
            fixed inset-y-0 left-0 z-40
            transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}
            md:transform-none md:relative md:z-auto
            transition-transform duration-300 ease-in-out
          `}>
            <Sidebar
              onSearchClick={() => setSearchModalOpen(true)}
              onClose={() => setSidebarOpen(false)}
              onConversationClick={() => setConversationModalOpen(true)}
              difyApi={difyApi!}
              currentApp={currentApp}
              isCollapsed={sidebarCollapsed}
            />
          </div>
        )}

        {/* 主要内容区域 */}
        <main className="flex-1 flex flex-col w-full md:w-auto relative" onClick={handleMainClick}>
          {/* 聊天详情页头部 - 移动端显示 */}
          <ChatDetailHeader
            title="NovaX AI"
            showBackButton={true}
            appIcon={appInfo?.appIcon}
            currentAppName={appInfo?.appName}
          />

          {/* Header 组件 - PC端显示，支持滚动隐藏，只覆盖主内容区域 */}
          {currentApp && (
            <div className={`md:block hidden absolute top-0 left-0 right-0 z-50 transition-transform duration-300 ease-in-out ${
              isHeaderVisible ? 'transform translate-y-0' : 'transform -translate-y-full'
            }`}>
              <Header
                onMenuClick={() => {
                  const isDesktop = window.innerWidth >= 768;
                  if (isDesktop) {
                    // PC端：如果侧边栏已打开，则切换折叠状态；如果关闭，则打开
                    if (sidebarOpen) {
                      setSidebarCollapsed(!sidebarCollapsed);
                    } else {
                      setSidebarOpen(true);
                      setSidebarCollapsed(false);
                    }
                  } else {
                    // 移动端：直接切换显示/隐藏
                    setSidebarOpen(!sidebarOpen);
                  }
                }}
                sidebarOpen={sidebarOpen}
                sidebarCollapsed={sidebarCollapsed}
                currentApp={currentApp}
                onAppSelect={onAppSelect}
                appList={appList}
                xAiApi={xAiApi}
                subStatusDetail={null}
                showSubscriptionModal={false}
                setShowSubscriptionModal={() => {}}
                appListSub={new Map()}
                setAppList={() => {}}
                fetchAllAppSubscriptions={() => Promise.resolve()}
              />
            </div>
          )}
          {/* 应用初始化状态 */}
          {isAppInitializing && (
            <div className="bg-blue-50 border-b border-blue-200 px-6 py-3 mt-16 md:mt-20">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-blue-700 text-sm">{t('chat.initializing')}</span>
              </div>
            </div>
          )}

          {/* 应用初始化错误 */}
          {appInitError && (
            <div className="bg-red-50 border-b border-red-200 px-6 py-3 mt-16 md:mt-20">
              <div className="flex items-center space-x-2">
                <svg className="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-red-700 text-sm">{appInitError}</span>
                <button
                  onClick={() => window.location.reload()}
                  className="text-red-600 hover:text-red-800 text-sm underline ml-2"
                >
                  {t('chat.reload')}
                </button>
              </div>
            </div>
          )}

          {/* 历史消息加载状态 */}
          {isLoadingHistory && (
            <div className="bg-blue-50 border-b border-blue-200 px-6 py-3 mt-16 md:mt-20">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-blue-700 text-sm">{t('chat.loadingHistory')}</span>
              </div>
            </div>
          )}

          {/* 历史消息加载错误 */}
          {historyLoadError && (
            <div className="bg-yellow-50 border-b border-yellow-200 px-6 py-3 mt-16 md:mt-20">
              <div className="flex items-center space-x-2">
                <svg className="h-4 w-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span className="text-yellow-700 text-sm">{historyLoadError}</span>
                {actualConversationId && (
                  <button
                    onClick={() => loadConversationHistory(actualConversationId)}
                    className="text-yellow-600 hover:text-yellow-800 text-sm underline ml-2"
                  >
                    {t('chat.retry')}
                  </button>
                )}
              </div>
            </div>
          )}

          {/* 主要内容 - 添加顶部间距避免被固定头部遮挡，允许滚动 */}
          <div className="pt-16 md:pt-20 flex-1 overflow-y-auto">
            <div className="h-full flex flex-col max-w-4xl mx-auto">
              {/* 消息列表区域 */}
              <MessageList messages={messages} />

              {/* 底部输入区域 */}
              <ChatInput
                inputValue={inputValue}
                isGenerating={isGenerating}
                onInputChange={setInputValue}
                onSubmit={handleSubmit}
                onKeyDown={handleKeyDown}
                onStop={handleStop}

                files={uploadFiles}
                onFilesChange={handleFilesChange}
                onFileUpload={handleFileUpload}
                allowedFileTypes={[
                  // 强制支持文档格式 - 不依赖appParam配置
                  'doc', 'docx', 'pdf', 'txt', 'md', 'xlsx', 'xls', 'pptx', 'ppt', 'csv',
                  'html', 'xml', 'epub', 'json',
                  // 图片格式
                  'jpg', 'jpeg', 'png', 'gif', 'webp', 'svg',
                  // 音频格式
                  'mp3', 'm4a', 'wav', 'webm', 'amr',
                  // 视频格式
                  'mp4', 'mov', 'mpeg', 'mpga'
                ]}
                isFileUploadEnabled={true} // 始终启用，不依赖appParam加载状态
                isUploading={isUploading}
                maxFileSize={appParam?.file_upload?.fileUploadConfig?.file_size_limit || 50}
                maxFiles={appParam?.file_upload?.number_limits || 2} // 使用动态配置值
                onValidationError={handleValidationError}
              />
            </div>
          </div>

          {/* 悬浮按钮 - 固定在屏幕左侧 */}
          <FloatingButtons />
        </main>

        {/* 搜索模态框 */}
        {searchModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">搜索</h3>
                <button
                  onClick={() => setSearchModalOpen(false)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </button>
              </div>
              <input
                type="text"
                placeholder="搜索..."
                className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
              />
            </div>
          </div>
        )}

        {/* 会话历史模态框 */}
        {conversationModalOpen && currentApp && difyApi && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
            onClick={() => setConversationModalOpen(false)}
          >
            <div
              className="bg-white rounded-2xl w-full max-w-4xl h-[80vh] flex flex-col shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              <ConversationsPage
                difyApi={difyApi}
                currentApp={currentApp}
                onClose={() => setConversationModalOpen(false)}
              />
            </div>
          </div>
        )}
      </div>
    </>
  )
}

export default ChatDetail
