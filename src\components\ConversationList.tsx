import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { DifyApi, IConversationItem } from '../api/src/dify-api';
import { useI18nRouter, useSimpleTranslation } from '../i18n/simple-hooks'

interface ApiProps {
  difyApi: DifyApi;
  appNameEn: string;
  onClose?: () => void;
}

// 历史提问记录组件
const ConversationList: React.FC<ApiProps> = ({ difyApi, appNameEn, onClose }) => {
  const navigate = useNavigate();
  const [conversationList, setConversationList] = useState<IConversationItem[]>([]);
  const [lastId, setLastId] = useState<string>('');
  const [hasMore, setHasMore] = useState<boolean>(true);
  const { currentLanguage, changeLanguage } = useI18nRouter()
  const { t } = useSimpleTranslation()

  // 获取更多历史提问
  const fetchMoreConversations = async () => {
    if (!hasMore) return;

    try {
      console.log('获取更多历史对话，使用appId:', difyApi.options.appId);
      const response = await difyApi.getConversationList({last_id: lastId});
      setConversationList((prevList) => [...prevList, ...response.data]);
      if (response.data.length > 0) {
        setLastId(response.data[response.data.length - 1].id);
      }
      setHasMore(response.has_more);
    } catch (error) {
      console.error('获取更多历史对话失败:', error);
    }
  };

  // 初次加载历史提问
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        console.log('初次加载历史对话，使用appId:', difyApi.options.appId);
        const response = await difyApi.getConversationList();
        setConversationList(response.data);
        setHasMore(response.has_more);
        if (response.data.length > 0) {
          setLastId(response.data[response.data.length - 1].id);
        }
        console.log('成功加载历史对话列表，数量:', response.data.length);
      } catch (error) {
        console.error('加载历史对话失败:', error);
      }
    };

    loadInitialData();
  }, [difyApi]);

  // 处理历史提问点击事件 - 直接跳转到目标详情页，避免中间路由闪烁
  const handleQuestionClick = (conversationId: string) => {
    // 先关闭弹窗
    onClose?.();
    // 使用replace避免在浏览器历史中留下中间状态
    navigate(`/${currentLanguage}/${appNameEn}/${conversationId}`, { replace: true });
  };

  // 获取分类颜色
  const getCategoryColor = (category: string) => {
    const colors: { [key: string]: string } = {
      '心血管医学': 'bg-red-100 text-red-700 border-red-200',
      '分子生物学': 'bg-blue-100 text-blue-700 border-blue-200',
      '肿瘤免疫学': 'bg-purple-100 text-purple-700 border-purple-200',
      '神经医学': 'bg-green-100 text-green-700 border-green-200',
      '病毒学': 'bg-yellow-100 text-yellow-700 border-yellow-200',
      '再生医学': 'bg-indigo-100 text-indigo-700 border-indigo-200',
    };
    return colors[category] || 'bg-gray-100 text-gray-700 border-gray-200';
  };

  // 格式化时间显示 timestamp格式 1750747538 - 支持国际化
  const formatTime = (timestamp: number): string => {
    const date = new Date(timestamp*1000);
    const now = new Date();

    // 检查是否为有效日期
    if (isNaN(date.getTime())) {
      return t('time.unknown');
    }

    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    // 防止未来时间导致负数
    if (diffDays < 0) {
      return t('time.justNow');
    }

    if (diffDays === 0) return t('time.today');
    if (diffDays === 1) return t('time.yesterday');
    if (diffDays < 7) return t('time.daysAgo').replace('{days}', diffDays.toString());

    // 根据语言环境格式化日期
    if (currentLanguage === 'zh') {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    } else {
      // 英文环境使用本地化日期格式
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    }
  };

  return (
    <div className="space-y-3">
      {conversationList.map((item) => (
        <div
          key={item.id}
          onClick={() => handleQuestionClick(item.id)}
          className="
            group cursor-pointer p-4 bg-white rounded-xl border border-gray-200 
            hover:border-blue-300 hover:shadow-md transition-all duration-200 ease-in-out
            transform hover:-translate-y-1
          "
        >

          <div className="flex items-start justify-between mb-3">
            <h4 className="
              text-sm md:text-base font-medium text-gray-900 
              group-hover:text-blue-600 transition-colors duration-200
              line-clamp-2 flex-1 mr-3
            ">
              {item.name}
            </h4>
          </div>

          {/* 时间 */}
          <div className="flex justify-end">
            <span className="text-xs text-gray-500">
              {formatTime(item.updated_at)}
            </span>
          </div>
        </div>
      ))}

      {/* 查看更多按钮 */}
      <div className="text-center pt-4">
        {hasMore ? (
          <button
            onClick={fetchMoreConversations}
            className="
              px-6 py-2 text-sm font-medium text-gray-600 bg-gray-50
              hover:bg-gray-100 hover:text-gray-700 rounded-xl
              transition-colors duration-200 border border-gray-200
            "
          >
            {t('history.viewMore')}
          </button>
        ) : (
          <p className="text-sm text-gray-500">{t('history.noMoreRecords')}</p>
        )}
      </div>
    </div>
  );
};

export default ConversationList;