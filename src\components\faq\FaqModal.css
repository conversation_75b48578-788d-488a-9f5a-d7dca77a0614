/* FAQ Modal 样式 */

/* 重置Modal的默认padding，让滚动条紧靠右边 */
.faq-modal .ant-modal-content {
  padding-right: 0 !important;
}

.faq-modal .ant-modal-body {
  padding-right: 0 !important;
}

.faq-collapse .ant-collapse-item {
  border-bottom: 1px solid #f0f0f0;
}

.faq-collapse .ant-collapse-item:last-child {
  border-bottom: none;
}

.faq-collapse .ant-collapse-header {
  padding: 16px 12px 16px 0 !important;
}

.faq-collapse .ant-collapse-content-box {
  padding: 0 12px 16px 0 !important;
}

.faq-collapse .ant-collapse-expand-icon {
  color: #1890ff;
}

.faq-collapse .ant-collapse-header:hover {
  background-color: #f8f9fa;
}

/* 滚动容器样式 - 让滚动条紧靠右边 */
.faq-scroll-container {
  padding-right: 0 !important;
  margin-right: 0 !important;
  /* 确保内容不会被滚动条遮挡 */
  box-sizing: border-box;
}

/* 自定义滚动条样式 */
.faq-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.faq-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.faq-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.faq-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
