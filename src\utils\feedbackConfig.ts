/**
 * 意见反馈配置工具函数
 * 用于获取不同环境下的projectId配置
 */

/**
 * 获取意见反馈的projectId
 * 根据域名判断环境并返回对应的projectId
 *
 * @returns {number} projectId
 */
export const getFeedbackProjectId = (): number => {
  // 根据域名判断环境
  const hostname = window.location.hostname

  // 生产环境域名
  if (hostname === 'ai.medsci.cn') {
    return 250
  }

  // 其他所有域名（包括localhost等开发环境和测试环境）
  return 193
}

/**
 * 获取意见反馈配置
 * 包含projectId和其他相关配置
 *
 * 环境配置说明（基于域名判断）：
 * - 生产环境 (ai.medsci.cn)：projectId = 250
 * - 其他所有域名（localhost、测试域名等）：projectId = 193
 */
export const getFeedbackConfig = () => {
  return {
    projectId: getFeedbackProjectId(),
    // 可以在这里添加其他配置项
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 3,
    supportedFormats: ['png', 'jpg', 'jpeg', 'gif']
  }
}
