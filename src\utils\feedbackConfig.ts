/**
 * 意见反馈配置工具函数
 * 用于获取不同环境下的projectId配置
 */

import { extractAppNameFromPath } from '../i18n/utils'

/**
 * 线上环境应用名称到projectId的映射表
 * 用于在生产环境中为不同应用分配不同的projectId
 */
const PRODUCTION_APP_PROJECT_ID_MAP: Record<string, number> = {
  'novax-base': 251,
  'novax-pro': 252,
  'elavax-base': 253,
  'elavax-pro': 254,
}

/**
 * 获取意见反馈的projectId
 * 根据域名和应用名称判断环境并返回对应的projectId
 *
 * @returns {number} projectId
 */
export const getFeedbackProjectId = (): number => {
  // 根据域名判断环境
  const hostname = window.location.hostname

  // 生产环境域名 - 根据应用名称分配不同的projectId
  if (hostname === 'ai.medsci.cn') {
    // 从当前URL路径中提取应用名称
    const appName = extractAppNameFromPath(window.location.pathname)
    

    // 查找应用对应的projectId
    const projectId = PRODUCTION_APP_PROJECT_ID_MAP[appName]

    if (projectId) {
      return projectId
    }

    // 如果无法识别应用名称，使用默认的生产环境projectId
    console.warn(`未识别的应用名称: ${appName}，使用默认projectId`)
    return 250 // 默认生产环境projectId
  }

  // 其他所有域名（包括localhost等开发环境和测试环境）
  return 193
}

/**
 * 获取意见反馈配置
 * 包含projectId和其他相关配置
 *
 * 环境配置说明（基于域名和应用名称判断）：
 * - 生产环境 (ai.medsci.cn)：
 *   - novax-base → projectId = 251
 *   - novax-pro → projectId = 252
 *   - elavax-base → projectId = 253
 *   - elavax-pro → projectId = 254
 *   - 其他/未识别应用 → projectId = 250（默认）
 * - 其他所有域名（localhost、测试域名等）：projectId = 193
 */
export const getFeedbackConfig = () => {
  return {
    projectId: getFeedbackProjectId(),
    // 可以在这里添加其他配置项
    maxFileSize: 10 * 1024 * 1024, // 10MB
    maxFiles: 3,
    supportedFormats: ['png', 'jpg', 'jpeg', 'gif']
  }
}
